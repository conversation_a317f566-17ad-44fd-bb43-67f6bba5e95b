/**
 * shenanPioneer 模块 API 类型定义
 */

import type { FlowType } from '../enum/flow'
import type { MessageResolveFlag, MessageStatus, MessageType } from '../enum/message'

/** 项目统计数据 */
export interface ProjectStatistics {
  /** 施工单位数量 */
  contractingUnitCount: number
  /** 工程总数 */
  totalProjectCount: number
  /** 施工中工程数量 */
  ongoingProjectCount: number
  /** 工程总金额（万元） */
  totalAmount: number
  /** 待整改工程数量 */
  rectifyProjectCount: number
}

/** 违规告警趋势数据项 */
export interface ViolationTrendItem {
  /** 事件数量 */
  eventCount: number
  /** 事件日期 */
  eventDate: string
}

/** 违规告警趋势请求参数 */
export interface ViolationTrendParams {
  /** 开始日期 */
  startDate?: string
  /** 结束日期 */
  endDate?: string
}

/** 工程区域分布数据项 */
export interface ProjectRegionItem {
  /** 工程数量 */
  projectCount: number
  /** 区域标题 */
  title: string
  /** 区域类型 */
  type: string
}

/** 工程区域分布请求参数 */
export interface ProjectRegionParams {
  /** 区域ID */
  regionId: string
  /** 区域父级ID */
  regionPid: string
  /** 区域子级ID */
  regionCid: string
}

/** 用户区域关联信息中的区域ID项 */
export interface UserRegionIdItem {
  /** 创建时间 */
  createdAt: number
  /** ID */
  id: string
  /** 是否删除 */
  isDeleted: number
  /** 区域父级ID */
  regionPid: string
  /** 关联ID */
  relateId: string
  /** 更新时间 */
  updatedAt: number
}

/** 用户区域关联信息 */
export interface UserRegionRelateInfo {
  /** 激活状态 */
  active: number
  /** 创建时间 */
  createdAt: number
  /** ID */
  id: string
  /** 级别 */
  level: number
  /** 成员ID */
  memberId: string
  /** 区域ID列表 */
  regionIds: UserRegionIdItem[]
  /** 类型ID */
  typeId: string
  /** 更新时间 */
  updatedAt: number
}

/** 消息提醒项 */
export interface MessageRemindItem {
  /** 消息内容 */
  content: string
  /** 创建时间 */
  createdAt: number
  /** 消息ID */
  id: string
  /** 订单ID */
  orderId: string
  /** 项目ID */
  projectId: string
  /** 关联ID */
  relateId: string
  /** 解决标志 */
  resolveFlag: MessageResolveFlag
  /** 状态：0-未读，1-已读 */
  status: MessageStatus
  /** 类型：6-接入监管，7-结束监管 */
  type: MessageType
  /** 更新时间 */
  updatedAt: number
  /** 用户ID */
  userId: string
}

/** 消息提醒请求参数 */
export interface MessageRemindParams extends SC.API.IndexBodyInterface {
  /** 消息类型，支持多个值 */
  type: number[]
  /** 状态：0-未读，1-已读 */
  status?: number
  /** 解决标志：0-未解决，2-已解决 */
  resolveFlag?: number
  /** 创建时间 */
  createdAt?: string
  /** 更新时间 */
  updatedAt?: string
}

/** 实时警告事件项 */
export interface WarningEventItem {
  /** 通道编码 */
  channelCode: string
  /** 创建时间 */
  createdAt: number
  /** 设备编码 */
  deviceCode: string
  /** 区域名称 */
  districtName: string
  /** 事件发生时间 */
  eventAt: number
  /** 事件编号 */
  eventNo: string
  /** 事件ID */
  id: string
  /** 监控编号 */
  monitorNo: string
  /** 图片URL */
  pics: string
  /** 项目地址 */
  projectAddress: string
  /** 项目类别名称 */
  projectCateName: string
  /** 项目ID */
  projectId: string
  /** 项目名称 */
  projectName: string
  /** 区域子级ID */
  regionCid: string
  /** 区域ID */
  regionId: string
  /** 区域父级ID */
  regionPid: string
  /** 场景编码 */
  sceneCode: string
  /** 街道名称 */
  streetName: string
  /** 违规类型名称 */
  typeName: string
  /** 更新时间 */
  updatedAt: number
  /** 村庄名称 */
  villageName: string
  /** 违规框信息 */
  violationBox: string
}

/** 实时警告事件请求参数 */
export interface WarningEventParams extends SC.API.IndexBodyInterface {
  /** 页面大小，默认10 */
  size?: number
  /** 页码，从0开始，默认0 */
  page?: number
}

/** 流程监控项 */
export interface FlowItem {
  /** 创建时间 */
  createdAt: number
  /** 创建用户 */
  createdUser: string
  /** 流程类型 */
  flow: FlowType
  /** 流程ID */
  flowId: string
  /** 流程项ID */
  id: string
  /** 组织ID */
  organizationId: string
  /** 项目地址 */
  projectAddress: string
  /** 项目ID */
  projectId: string
  /** 项目名称 */
  projectName: string
  /** 项目编号 */
  projectNumber: string
  /** 项目POI ID */
  projectPoiId: string
  /** 区域子级ID */
  regionCid: string
  /** 区域ID */
  regionId: string
  /** 区域父级ID */
  regionPid: string
  /** 更新时间 */
  updatedAt: number
  /** 勘察时间 */
  inspectTime?: number
  /** 安装数量 */
  installCnt?: number
  /** 安装状态 */
  installStatus?: number
  /** 回收状态 */
  recycleStatus?: number
  /** 预约时间 */
  reservationTime?: number
}

/** 流程监控请求参数 */
export interface FlowParams extends SC.API.IndexBodyInterface {
  /** 页面大小，默认10 */
  size?: number
  /** 页码，从0开始，默认0 */
  page?: number
  /** 流程类型 */
  flow?: FlowType
}
