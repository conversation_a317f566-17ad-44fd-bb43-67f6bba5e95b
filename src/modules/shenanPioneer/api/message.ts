import type { LrEnum } from '@shencom/api'
import type { MessageRemindItem, MessageRemindParams } from './types'
import { ApiQueryConstruct, OperateEnum } from '@shencom/api'
/**
 * 消息提醒相关 API
 */
import { http } from '@shencom/request'
import { isNotNil } from 'es-toolkit'
import { MessageResolveFlag, MessageStatus, MessageType } from '../enum/message'
import { url } from './config'

/**
 * 获取消息提醒列表
 * @param params 请求参数
 * @returns 消息提醒响应数据
 */
export async function fetchMessageRemindList(params: MessageRemindParams) {
  params = params || {}
  params.size = params.size || 10
  params.page = params.page || 0
  let exps: SC.API.Query[] = []
  const query: SC.API.IndexQuery = []
  const queryItem: [any, string, OperateEnum?, LrEnum?][] = []

  if (isNotNil(params.status)) {
    queryItem.push([params.status, 'status', OperateEnum.EQ])
  }
  if (isNotNil(params.type)) {
    queryItem.push([params.type.join(','), 'type', OperateEnum.IN])
  }
  if (isNotNil(params.resolveFlag)) {
    queryItem.push([params.resolveFlag, 'resolveFlag', OperateEnum.EQ])
  }
  if (isNotNil(params.createdAt)) {
    queryItem.push([params.createdAt, 'createdAt', OperateEnum.BTW])
  }

  if (queryItem.length > 0) {
    exps = ApiQueryConstruct(queryItem) as SC.API.Query[]
    query.push({ exps })
  }

  params.query = query

  const res = await http.post<SC.API.IndexInterface<MessageRemindItem>>(`${url}/xsgc/message/remind/index`, params)

  return res.data
}

/**
 * 获取我的待阅消息
 * @param dateRange 日期范围
 * @returns 消息提醒响应数据
 */
export async function fetchUnreadMessages(dateRange?: string) {
  const params: MessageRemindParams = {
    type: [MessageType.已接入监管, MessageType.结束监管], // 接入监管和结束监管
    status: MessageStatus.未读, // 未读
    size: 5,
    createdAt: dateRange,
  }

  return fetchMessageRemindList(params)
}

/**
 * 获取我的已阅消息
 * @param dateRange 日期范围
 * @returns 消息提醒响应数据
 */
export async function fetchReadMessages(dateRange?: string) {
  const params: MessageRemindParams = {
    type: [MessageType.已接入监管, MessageType.结束监管], // 接入监管和结束监管
    status: MessageStatus.已读, // 已读
    size: 5,
    createdAt: dateRange,
  }

  return fetchMessageRemindList(params)
}

/**
 * 获取我的待办消息
 * @param dateRange 日期范围
 * @returns 消息提醒响应数据
 */
export async function fetchTodoMessages(dateRange?: string) {
  const params: MessageRemindParams = {
    type: [
      MessageType.预约安装,
      MessageType.预约回收,
      MessageType.现场勘察,
      MessageType.上门安装,
      MessageType.上门回收,
    ],
    resolveFlag: MessageResolveFlag.待办, // 未解决
    size: 5,
    createdAt: dateRange,
  }

  return fetchMessageRemindList(params)
}

/**
 * 获取我的已办消息
 * @param dateRange 日期范围
 * @returns 消息提醒响应数据
 */
export async function fetchDoneMessages(dateRange?: string) {
  const params: MessageRemindParams = {
    type: [
      MessageType.预约安装,
      MessageType.预约回收,
      MessageType.现场勘察,
      MessageType.上门安装,
      MessageType.上门回收,
    ], // 待办事项类型
    resolveFlag: MessageResolveFlag.本人已办, // 已解决
    size: 5,
    updatedAt: dateRange,
  }

  return fetchMessageRemindList(params)
}
