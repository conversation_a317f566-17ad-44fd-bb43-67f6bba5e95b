import type { UserRole } from '../api/user'
import { fetchUserRole } from '../api/user'
import { Role, RoleType } from '../enum/role'

/**
 * 分为3种角色
 * - 组织人员角色
 * - 业务人员角色
 * - 工程人员角色
 */
const roleTypeMap: Record<RoleType, Set<string>> = {
  [RoleType.ORGANIZATION]: new Set([Role.ORDINARY_INSPECTOR, Role.ORDINARY_CITY_ADMINISTRATOR, Role.ORDINARY_DISTRICT_ADMINISTRATOR, Role.ORDINARY_STREET_ADMINISTRATOR, Role.ORDINARY_COMMUNITY_ADMINISTRATOR, Role.SENIOR_INSPECTOR, Role.SENIOR_CITY_ADMINISTRATOR, Role.SENIOR_DISTRICT_ADMINISTRATOR, Role.SENIOR_STREET_ADMINISTRATOR, Role.SENIOR_COMMUNITY_ADMINISTRATOR, Role.ADMINISTRATOR]),
  [RoleType.BUSINESS]: new Set([Role.TECHNICAL_DIRECTOR, Role.COMMERCIAL_AFFAIRS, Role.HEAD_SALES, Role.INSTALLATION_MANAGER]),
  [RoleType.ENGINEERING]: new Set([Role.CONSTRUCTION_PARTY, Role.CONSTRUCTION_UNIT_LEADER]),
}

export function useMember() {
  // 缓存数据
  const userRole: Ref<string[]> = ref([])
  // 当前用户角色
  const currentRole: Ref<RoleType | null> = ref(null)

  /**
   * 获取用户角色
   */
  const getUserRole = async (params?: { organizationId?: string }) => {
    if (userRole.value.length === 0) {
      const res = await fetchUserRole(params)
      userRole.value = res.map(role => role.roleName)
    }
    return userRole.value
  }

  // 判断用户是什么类型的角色
  const isOrganizationRole = (userRole: string[]) => {
    return userRole.some(role => roleTypeMap[RoleType.ORGANIZATION].has(role))
  }

  const isBusinessRole = (userRole: string[]) => {
    return userRole.some(role => roleTypeMap[RoleType.BUSINESS].has(role))
  }

  const isEngineeringRole = (userRole: string[]) => {
    return userRole.some(role => roleTypeMap[RoleType.ENGINEERING].has(role))
  }

  const getRoleType = async () => {
    const userRole = await getUserRole()
    let type = null
    if (isOrganizationRole(userRole)) {
      type = RoleType.ORGANIZATION
    }
    if (isBusinessRole(userRole)) {
      type = RoleType.BUSINESS
    }
    if (isEngineeringRole(userRole)) {
      type = RoleType.ENGINEERING
    }

    currentRole.value = type

    return type
  }

  return { getRoleType, currentRole }
}
