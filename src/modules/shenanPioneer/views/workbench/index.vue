<route lang="yaml">
meta:
  title: 工作台
  icon: i-ant-design:home-twotone
</route>

<!--
  工作台首页
  集成数据概览、快捷功能、待办事项、待阅事项、图表等组件
-->
<script setup lang="ts">
import { RoleType } from '../../enum/role'
import { useMember } from '../../hooks/useMember'
import AlarmTrendChart from './components/AlarmTrendChart.vue'
import DataOverview from './components/DataOverview.vue'
import PendingReservationList from './components/PendingReservationList.vue'
import PendingServiceList from './components/PendingServiceList.vue'
import ProjectDistributionChart from './components/ProjectDistributionChart.vue'
import QuickActions from './components/QuickActions.vue'
import ReadingTasks from './components/ReadingTasks.vue'
import RectificationTasks from './components/RectificationTasks.vue'
import TodoTasks from './components/TodoTasks.vue'
import WarningEvent from './components/WarningEvent.vue'

const { getRoleType, currentRole } = useMember()
// 获取用角色

const isOrganizationOrEngineering = computed(() => {
  return currentRole.value && [RoleType.ORGANIZATION, RoleType.ENGINEERING].includes(currentRole.value)
})

const isBusiness = computed(() => {
  return currentRole.value && currentRole.value === RoleType.BUSINESS
})

const isEngineering = computed(() => {
  return currentRole.value && currentRole.value === RoleType.ENGINEERING
})

const isOrganization = computed(() => {
  return currentRole.value && currentRole.value === RoleType.ORGANIZATION
})

onMounted(async () => {
  await getRoleType()
})
</script>

<template>
  <div class="relative grid grid-cols-2 gap-4 p-4">
    <div class="absolute left-10 top-0 z-100 text-red">
      当前角色：{{ currentRole }}
    </div>

    <!-- 数据概览区域 -->
    <section class="grid gap-4">
      <DataOverview />
      <QuickActions />
    </section>

    <!-- 待办事项、待阅事项 -->
    <section
      v-if="isOrganizationOrEngineering"
      class="grid grid-cols-2 gap-4"
    >
      <!-- 待办事项 -->
      <TodoTasks />
      <!-- 待阅事项 -->
      <ReadingTasks />
    </section>

    <template v-if="isBusiness">
      <!-- 待预约清单 -->
      <PendingReservationList />
      <!-- 待服务清单 -->
      <PendingServiceList />
    </template>

    <template v-if="isEngineering">
      <!-- 实时警告 -->
      <WarningEvent />
      <!-- 整改清单 -->
      <RectificationTasks />
    </template>

    <template v-if="isOrganization">
      <!-- 数据分析区域 -->
      <AlarmTrendChart />
      <!-- 项目分布图 -->
      <ProjectDistributionChart />
    </template>
  </div>
</template>
